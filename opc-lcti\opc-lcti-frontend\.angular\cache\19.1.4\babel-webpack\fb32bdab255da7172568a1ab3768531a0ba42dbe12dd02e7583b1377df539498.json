{"ast": null, "code": "var hash = {\n  start: 'end',\n  end: 'start'\n};\nexport default function getOppositeVariationPlacement(placement) {\n  return placement.replace(/start|end/g, function (matched) {\n    return hash[matched];\n  });\n}", "map": {"version": 3, "names": ["hash", "start", "end", "getOppositeVariationPlacement", "placement", "replace", "matched"], "sources": ["C:/_git/LCTI/opc-lcti/opc-lcti-frontend/node_modules/@popperjs/core/lib/utils/getOppositeVariationPlacement.js"], "sourcesContent": ["var hash = {\n  start: 'end',\n  end: 'start'\n};\nexport default function getOppositeVariationPlacement(placement) {\n  return placement.replace(/start|end/g, function (matched) {\n    return hash[matched];\n  });\n}"], "mappings": "AAAA,IAAIA,IAAI,GAAG;EACTC,KAAK,EAAE,KAAK;EACZC,GAAG,EAAE;AACP,CAAC;AACD,eAAe,SAASC,6BAA6BA,CAACC,SAAS,EAAE;EAC/D,OAAOA,SAAS,CAACC,OAAO,CAAC,YAAY,EAAE,UAAUC,OAAO,EAAE;IACxD,OAAON,IAAI,CAACM,OAAO,CAAC;EACtB,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}